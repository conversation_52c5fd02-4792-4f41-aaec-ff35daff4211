# Config模块演示

这个演示展示了新的Config模块的功能，包括通过CLI进行配置管理和启动前的配置验证。

## 功能特性

1. **配置状态管理**：
   - `NotConfigured`：未配置状态（默认）
   - `Configured`：已配置但未验证
   - `Validated`：已配置且验证通过

2. **启动前检查**：
   - 如果没有用户配置，系统不能启动
   - 必须通过配置验证才能启动

3. **CLI配置命令**：
   - `config show`：显示当前配置
   - `config set <path>`：从TOML/JSON文件设置配置
   - `config validate`：验证配置
   - `config reset`：重置配置
   - `config load <path>`：从文件加载配置
   - `config save <path>`：保存配置到文件（支持TOML/JSON格式）

## 演示步骤

### 1. 启动应用程序
```bash
cargo run
```

### 2. 尝试启动（应该失败）
在CLI中输入：
```
start
```
应该看到错误信息，提示需要先配置系统。

### 3. 查看当前配置状态
```
config show
```
应该显示默认配置和"Not Configured"状态。

### 4. 从TOML文件设置配置
```
config set example_config.toml
```

### 5. 验证配置
```
config validate
```

### 6. 再次查看配置状态
```
config show
```
现在应该显示"Validated"状态和"User Configured: Yes"。

### 7. 现在可以启动系统
```
start
```
应该成功启动。

### 8. 保存配置到TOML文件
```
config save my_config.toml
```

### 9. 重置配置
```
config reset
```

### 10. 从文件加载配置
```
config load example_config.toml
config validate
```

## 支持的配置格式

系统支持两种配置文件格式：

1. **TOML格式**（推荐）：
   - 文件扩展名：`.toml`
   - 更易读，支持注释
   - 示例：`example_config.toml`

2. **JSON格式**：
   - 文件扩展名：`.json`
   - 标准JSON格式
   - 示例：`example_config.json`

## 配置项说明

- `exchange`：交易所名称（如"Binance"）
- `start_time`：回测开始时间（ISO 8601格式）
- `end_time`：回测结束时间（ISO 8601格式）
- `data_path`：数据文件路径
- `websocket_port`：WebSocket服务器端口
- `http_port`：HTTP服务器端口
- `log_level`：日志级别（debug, info, warn, error）
- `performance_target_us`：性能目标（微秒）

## 验证规则

1. 开始时间必须早于结束时间
2. 数据路径必须存在
3. WebSocket端口和HTTP端口必须不同
