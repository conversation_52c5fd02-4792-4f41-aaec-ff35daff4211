# Config模块演示

这个演示展示了新的Config模块的功能，包括通过CLI进行配置管理和启动前的配置验证。

## 功能特性

1. **配置状态管理**：
   - `NotConfigured`：未配置状态（默认）
   - `Configured`：已配置但未验证
   - `Validated`：已配置且验证通过

2. **启动前检查**：
   - 如果没有用户配置，系统不能启动
   - 必须通过配置验证才能启动

3. **CLI配置命令**：
   - `config show`：显示当前配置
   - `config set <key> <value>`：设置配置项
   - `config validate`：验证配置
   - `config reset`：重置配置
   - `config load <path>`：从文件加载配置
   - `config save <path>`：保存配置到文件

## 演示步骤

### 1. 启动应用程序
```bash
cargo run
```

### 2. 尝试启动（应该失败）
在CLI中输入：
```
start
```
应该看到错误信息，提示需要先配置系统。

### 3. 查看当前配置状态
```
config show
```
应该显示默认配置和"Not Configured"状态。

### 4. 设置配置项
```
config set websocket_port 9090
config set log_level debug
```

### 5. 验证配置
```
config validate
```

### 6. 再次查看配置状态
```
config show
```
现在应该显示"Validated"状态和"User Configured: Yes"。

### 7. 现在可以启动系统
```
start
```
应该成功启动。

### 8. 保存配置到文件
```
config save my_config.json
```

### 9. 重置配置
```
config reset
```

### 10. 从文件加载配置
```
config load example_config.json
config validate
```

## 配置项说明

- `websocket_port` / `ws_port`：WebSocket服务器端口
- `http_port`：HTTP服务器端口
- `log_level`：日志级别
- `data_path`：数据文件路径
- `performance_target_us`：性能目标（微秒）

## 验证规则

1. 开始时间必须早于结束时间
2. 数据路径必须存在
3. WebSocket端口和HTTP端口必须不同
